#!/usr/bin/env python3
"""
启动完整应用的脚本
"""
import os
import sys
import subprocess
import time
import threading
import signal

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
    
    # 使用subprocess启动uvicorn
    cmd = [
        sys.executable, "-m", "uvicorn", 
        "main:app", 
        "--host", "127.0.0.1", 
        "--port", "8001",
        "--log-level", "info"
    ]
    
    try:
        process = subprocess.Popen(
            cmd,
            cwd=backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 读取输出
        for line in iter(process.stdout.readline, ''):
            print(f"[Backend] {line.strip()}")
            if "Uvicorn running on" in line:
                print("✅ 后端服务启动成功!")
                break
                
        return process
    except Exception as e:
        print(f"❌ 后端启动失败: {e}")
        return None

def start_frontend():
    """启动前端应用"""
    print("🎨 启动前端应用...")
    frontend_dir = os.path.join(os.path.dirname(__file__), 'frontend')
    
    cmd = ["npm", "start"]
    
    try:
        process = subprocess.Popen(
            cmd,
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True
        )
        return process
    except Exception as e:
        print(f"❌ 前端启动失败: {e}")
        return None

def main():
    """主函数"""
    print("🌟 启动加密货币量化交易K线图应用")
    print("=" * 60)
    
    backend_process = None
    frontend_process = None
    
    try:
        # 启动后端
        backend_process = start_backend()
        if not backend_process:
            return
        
        # 等待后端启动
        time.sleep(3)
        
        # 启动前端
        frontend_process = start_frontend()
        if not frontend_process:
            return
        
        print("=" * 60)
        print("🎉 应用启动完成!")
        print("📍 后端API: http://127.0.0.1:8001")
        print("📖 API文档: http://127.0.0.1:8001/docs")
        print("🎨 前端应用: 请查看Electron窗口")
        print("⏹️  按 Ctrl+C 停止所有服务")
        print("=" * 60)
        
        # 等待进程
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")
        
    finally:
        # 清理进程
        if backend_process:
            backend_process.terminate()
            print("✅ 后端服务已停止")
            
        if frontend_process:
            frontend_process.terminate()
            print("✅ 前端应用已停止")

if __name__ == "__main__":
    main()
